{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/vite": "^4.1.11", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "daisyui": "^5.0.35", "file-saver": "^2.0.5", "framer-motion": "^12.9.7", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-toastify": "^11.0.5", "serve": "^14.2.4", "tailwindcss": "^4.1.11", "three": "^0.176.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "sharp": "^0.34.1", "terser": "^5.42.0", "vite": "^6.3.5", "vite-imagetools": "^7.1.0", "vite-plugin-compression2": "^2.0.1"}}