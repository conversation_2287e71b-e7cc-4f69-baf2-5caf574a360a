/* Footer Styles */
.footer {
  width: 100%;
  margin-top: auto;
  transition: all 0.3s ease;
}

.footer-dark {
  background-color: #111827;
  color: #f3f4f6;
  border-top: 1px solid #374151;
}

.footer-light {
  background-color: #f8fafc;
  color: #64748b;
  border-top: 1px solid #e5e7eb;
}

.footer-container {
  max-width: 1200px;
  width: 92%;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Footer grid layout */
.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

/* Brand section */
.brand-section {
  margin-bottom: 1.5rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.brand-name {
  margin-left: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.brand-description {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 0.925rem;
}

/* Newsletter subscription */
.newsletter-form {
  display: flex;
}

.newsletter-input {
  flex: 1;
  padding: 0.625rem 0.75rem;
  border-radius: 0.375rem 0 0 0.375rem;
  border-right: none;
  font-size: 0.875rem;
  outline: none;
}

.newsletter-button {
  padding: 0.625rem 1rem;
  border-radius: 0 0.375rem 0.375rem 0;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Footer columns */
.footer-column {
  margin-bottom: 1rem;
}

.column-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.footer-links {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #3b82f6 !important;
}

/* Footer bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  text-align: center;
}

.copyright {
  font-size: 0.875rem;
}

/* Social links */
.social-links {
  display: flex;
  gap: 1.25rem;
  align-items: center;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.social-link:hover {
  color: #3b82f6 !important;
}

/* Responsive styles */
@media (min-width: 640px) {
  .footer-grid {
    grid-template-columns: 1fr 2fr;
    align-items: start;
  }
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr 3fr;
  }
  
  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

@media (min-width: 1024px) {
  .footer-container {
    padding: 2.5rem 1rem 1.5rem;
  }
} 