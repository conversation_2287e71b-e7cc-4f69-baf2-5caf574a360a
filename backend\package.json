{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "bullmq": "^5.52.2", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^7.0.2", "openai": "^4.97.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "sharp": "^0.33.3", "three": "^0.176.0", "validator": "^13.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}